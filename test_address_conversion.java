import org.dromara.common.scanning.utils.TronAddressUtils;

public class TestAddressConversion {
    public static void main(String[] args) {
        // 测试地址转换
        String hexAddress = "41b98f22154e274c6abf194a84bed33b42cafa1e08";
        
        try {
            String base58Address = TronAddressUtils.hexToAddressFull(hexAddress);
            System.out.println("十六进制地址: " + hexAddress);
            System.out.println("Base58地址: " + base58Address);
            
            // 验证是否是期望的地址
            String expectedAddress = "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs";
            System.out.println("期望地址: " + expectedAddress);
            System.out.println("转换正确: " + base58Address.equals(expectedAddress));
            
        } catch (Exception e) {
            System.err.println("转换失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
