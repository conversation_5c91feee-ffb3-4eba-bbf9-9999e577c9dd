package org.dromara.wallet.wallet.monitor.tron.event;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.scanning.chain.model.TransactionModel;
import org.dromara.common.scanning.chain.model.tron.TronContractModel;
import org.dromara.common.scanning.chain.model.tron.TronLogModel;
import org.dromara.common.scanning.monitor.TronMonitorEvent;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.wallet.config.facade.TronConfigFacade;
import org.dromara.wallet.service.IMetaTrc20CstaddressinfoService;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * TRON地址预过滤事件
 * 负责检查交易是否涉及监控地址，实现早期过滤以提升性能
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>地址预过滤：检查交易是否涉及监控的钱包地址或合约地址</li>
 *   <li>性能优化：早期过滤减少后续不必要的API调用</li>
 *   <li>支持TRC20和TRX原生转账的地址检查</li>
 *   <li>设置处理标记，供后续MonitorEvent参考</li>
 *   <li>地址监控逻辑：集成了从TronTransactionManager移动过来的地址监控功能</li>
 * </ul>
 *
 * <p>重构说明：</p>
 * <ul>
 *   <li>移除对TronTransactionManager的依赖，直接依赖IMetaTrc20CstaddressinfoService</li>
 *   <li>将getMonitoredAddresses和isMonitoredTransaction方法从TronTransactionManager移动到此处</li>
 *   <li>实现自包含的地址监控逻辑，消除循环依赖</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/24
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TronAddressFilterEvent implements TronMonitorEvent {

    private final IMetaTrc20CstaddressinfoService tronAddressService;
    private final TronConfigFacade configFacade;

    /**
     * 地址过滤结果存储键名
     */
    public static final String ADDRESS_FILTER_RESULT_KEY = "tron.addressFilterPassed";



    @Override
    public void call(TransactionModel transactionModel) {
        try {
            // 检查是否为TRON交易模型
            if (transactionModel.getTronTransactionModel() == null) {
                log.debug("{}链跳过非TRON交易模型", configFacade.getChainName());
                return;
            }

            // 获取TRON交易模型
            org.dromara.common.scanning.chain.model.tron.TronTransactionModel tronTxModel =
                transactionModel.getTronTransactionModel();

            String txId = tronTxModel.getTxID();
            if (txId == null) {
                log.debug("{}链交易ID为空，跳过处理", configFacade.getChainName());
                return;
            }

            // 执行地址预过滤
            boolean shouldProcess = shouldProcessTransaction(tronTxModel);

            // 存储地址过滤结果到处理上下文
            storeAddressFilterResult(transactionModel, shouldProcess);

            if (shouldProcess) {
                log.debug("{}链交易{}通过地址过滤", configFacade.getChainName(), txId);
            } else {
                log.debug("{}链交易{}不涉及监控地址，跳过处理", configFacade.getChainName(), txId);
            }

        } catch (Exception e) {
            log.error("{}链地址过滤处理失败: {}", configFacade.getChainName(), e.getMessage());
            // 异常时存储false，表示过滤失败
            storeAddressFilterResult(transactionModel, false);
        }
    }

    /**
     * 检查交易是否需要处理（地址预过滤）
     * 改进版本：支持从rawDataHex解析数据，处理各种交易类型
     */
    private boolean shouldProcessTransaction(org.dromara.common.scanning.chain.model.tron.TronTransactionModel tronTxModel) {
        // 获取监控地址集合
        Set<String> monitoredContractAddresses = getMonitoredContractAddresses();
        Set<String> monitoredWalletAddresses = getMonitoredWalletAddresses();

        if (monitoredContractAddresses.isEmpty() && monitoredWalletAddresses.isEmpty()) {
            log.debug("{}链没有配置监控地址，跳过交易处理", configFacade.getChainName());
            return false;
        }

        // 提取交易中的所有相关地址
        List<String> transactionAddresses = extractAllAddressesFromTransaction(tronTxModel);

        if (transactionAddresses.isEmpty()) {
            log.debug("{}链交易{}无法提取地址信息，保守处理",
                configFacade.getChainName(), tronTxModel.getTxID());
            return true; // 无法提取地址时保守处理
        }

        // 检查是否有任何地址在监控列表中
        for (String address : transactionAddresses) {
            if (containsMonitoredAddress(address, monitoredWalletAddresses) ||
                containsMonitoredAddress(address, monitoredContractAddresses)) {
                log.debug("{}链交易{}包含监控地址: {}",
                    configFacade.getChainName(), tronTxModel.getTxID(), address);
                return true;
            }
        }

        log.debug("{}链交易{}不包含监控地址，提取到的地址: {}",
            configFacade.getChainName(), tronTxModel.getTxID(), transactionAddresses);
        return false;
    }

    /**
     * 从交易中提取所有相关地址
     * 支持多种交易类型和数据源
     */
    private List<String> extractAllAddressesFromTransaction(org.dromara.common.scanning.chain.model.tron.TronTransactionModel tronTxModel) {
        List<String> addresses = new ArrayList<>();

        try {
            // 方法1: 从rawData对象提取（优先）
            if (extractFromRawData(tronTxModel, addresses)) {
                log.debug("{}链交易{}从rawData提取到地址: {}",
                    configFacade.getChainName(), tronTxModel.getTxID(), addresses);
                return normalizeAddresses(addresses);
            }

            // 方法2: 从rawDataHex解析（备用）
            if (extractFromRawDataHex(tronTxModel, addresses)) {
                log.debug("{}链交易{}从rawDataHex提取到地址: {}",
                    configFacade.getChainName(), tronTxModel.getTxID(), addresses);
                return normalizeAddresses(addresses);
            }

        } catch (Exception e) {
            log.warn("{}链交易{}地址提取失败: {}",
                configFacade.getChainName(), tronTxModel.getTxID(), e.getMessage());
        }

        return normalizeAddresses(addresses);
    }

    /**
     * 从rawData对象提取地址
     */
    private boolean extractFromRawData(org.dromara.common.scanning.chain.model.tron.TronTransactionModel tronTxModel, List<String> addresses) {
        try {
            if (tronTxModel.getRawData() == null ||
                tronTxModel.getRawData().getContract() == null ||
                tronTxModel.getRawData().getContract().isEmpty()) {
                return false;
            }

            TronContractModel contract = tronTxModel.getRawData().getContract().get(0);
            String contractType = contract.getType();

            log.debug("{}链交易{}合约类型: {}",
                configFacade.getChainName(), tronTxModel.getTxID(), contractType);

            if ("TransferContract".equals(contractType)) {
                // 原生TRX转账
                return extractFromTransferContract(contract, addresses);
            } else if ("TriggerSmartContract".equals(contractType)) {
                // TRC20代币转账或其他智能合约调用
                return extractFromTriggerSmartContract(contract, addresses);
            } else {
                // 其他类型的合约，尝试通用提取
                return extractFromGenericContract(contract, addresses);
            }

        } catch (Exception e) {
            log.debug("从rawData提取地址失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 从TransferContract提取地址（原生TRX转账）
     */
    private boolean extractFromTransferContract(TronContractModel contract, List<String> addresses) {
        try {
            if (contract.getParameter() != null && contract.getParameter().getValue() != null) {
                String ownerAddress = contract.getParameter().getValue().getOwnerAddress();
                String toAddress = contract.getParameter().getValue().getToAddress();

                // 添加from地址
                if (ownerAddress != null && !ownerAddress.trim().isEmpty()) {
                    addresses.add(ownerAddress);
                }
                // 添加to地址
                if (toAddress != null && !toAddress.trim().isEmpty()) {
                    addresses.add(toAddress);
                }

                return !addresses.isEmpty();
            }
        } catch (Exception e) {
            log.debug("从TransferContract提取地址失败: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 从TriggerSmartContract提取地址（TRC20转账等）
     */
    private boolean extractFromTriggerSmartContract(TronContractModel contract, List<String> addresses) {
        try {
            if (contract.getParameter() != null && contract.getParameter().getValue() != null) {
                // 添加调用者地址（from地址）
                String ownerAddress = contract.getParameter().getValue().getOwnerAddress();
                if (ownerAddress != null && !ownerAddress.trim().isEmpty()) {
                    log.debug("提取到ownerAddress: {}", ownerAddress);
                    addresses.add(ownerAddress);
                }

                // 添加合约地址（contract地址）
                String contractAddress = contract.getParameter().getValue().getToAddress();
                if (contractAddress != null && !contractAddress.trim().isEmpty()) {
                    log.debug("提取到contractAddress: {}", contractAddress);
                    addresses.add(contractAddress);
                } else {
                    log.debug("contractAddress为null，检查其他字段");
                    // 检查是否有其他字段包含合约地址
                    log.debug("contract.getParameter().getValue()的所有字段: ownerAddress={}, toAddress={}, data={}",
                        contract.getParameter().getValue().getOwnerAddress(),
                        contract.getParameter().getValue().getToAddress(),
                        contract.getParameter().getValue().getData());
                }

                // 解析data字段中的transfer函数参数，提取真正的接收地址（to地址）
                extractTrc20TransferAddresses(contract, addresses);

                return !addresses.isEmpty();
            }
        } catch (Exception e) {
            log.debug("从TriggerSmartContract提取地址失败: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 从TRC20转账的data字段中提取接收地址
     * TRC20 transfer函数格式: transfer(address to, uint256 amount)
     * ABI编码格式:
     * - 前4字节: 函数选择器 (0xa9059cbb)
     * - 接下来32字节: to地址（左填充0）
     * - 最后32字节: 金额
     */
    private void extractTrc20TransferAddresses(TronContractModel contract, List<String> addresses) {
        try {
            // 获取data字段
            String data = getContractData(contract);
            if (data == null || data.length() < 10) {
                return;
            }

            // 移除0x前缀
            if (data.startsWith("0x")) {
                data = data.substring(2);
            }

            // 检查是否是transfer函数调用 (函数选择器: a9059cbb)
            if (data.length() >= 8 && data.substring(0, 8).equalsIgnoreCase("a9059cbb")) {
                // 提取to地址（跳过函数选择器4字节，取接下来32字节中的地址部分）
                if (data.length() >= 72) { // 8 + 64 字符
                    String toAddressHex = data.substring(8, 72); // 64字符的地址数据

                    // 取后40位作为地址（前24位是填充的0）
                    if (toAddressHex.length() >= 40) {
                        String addressPart = toAddressHex.substring(24); // 取后40位

                        // 如果地址不全是0，则添加到地址集合
                        if (!addressPart.matches("^0+$")) {
                            // 添加41前缀构成完整的TRON十六进制地址
                            String fullTronAddress = "41" + addressPart;
                            addresses.add(fullTronAddress);

                            log.debug("从TRC20 transfer data中提取到接收地址: {}", fullTronAddress);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.debug("解析TRC20转账data字段失败: {}", e.getMessage());
        }
    }

    /**
     * 获取合约调用的data字段
     */
    private String getContractData(TronContractModel contract) {
        try {
            if (contract.getParameter() != null &&
                contract.getParameter().getValue() != null) {
                return contract.getParameter().getValue().getData();
            }
        } catch (Exception e) {
            log.debug("获取合约data字段失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 从通用合约提取地址
     */
    private boolean extractFromGenericContract(TronContractModel contract, List<String> addresses) {
        try {
            if (contract.getParameter() != null && contract.getParameter().getValue() != null) {
                String ownerAddress = contract.getParameter().getValue().getOwnerAddress();
                String toAddress = contract.getParameter().getValue().getToAddress();

                // 添加from地址
                if (ownerAddress != null && !ownerAddress.trim().isEmpty()) {
                    addresses.add(ownerAddress);
                }
                // 添加to地址
                if (toAddress != null && !toAddress.trim().isEmpty()) {
                    addresses.add(toAddress);
                }

                return !addresses.isEmpty();
            }
        } catch (Exception e) {
            log.debug("从通用合约提取地址失败: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 从rawDataHex解析地址（备用方案）
     * 当rawData对象不可用时使用
     */
    private boolean extractFromRawDataHex(org.dromara.common.scanning.chain.model.tron.TronTransactionModel tronTxModel, List<String> addresses) {
        try {
            String rawDataHex = tronTxModel.getRawDataHex();
            if (rawDataHex == null || rawDataHex.trim().isEmpty()) {
                return false;
            }

            // 移除0x前缀
            if (rawDataHex.startsWith("0x")) {
                rawDataHex = rawDataHex.substring(2);
            }

            // 简单的地址模式匹配（TRON地址在hex中的模式）
            // TRON地址通常以41开头，后跟40位十六进制
            java.util.regex.Pattern addressPattern = java.util.regex.Pattern.compile("41[0-9a-fA-F]{40}");
            java.util.regex.Matcher matcher = addressPattern.matcher(rawDataHex);

            while (matcher.find()) {
                String hexAddress = matcher.group();
                addresses.add(hexAddress);
            }

            log.debug("{}链交易{}从rawDataHex提取到{}个地址",
                configFacade.getChainName(), tronTxModel.getTxID(), addresses.size());

            return !addresses.isEmpty();

        } catch (Exception e) {
            log.debug("从rawDataHex提取地址失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 标准化地址格式
     * 将十六进制地址转换为Base58格式，并统一为小写
     */
    private List<String> normalizeAddresses(List<String> addresses) {
        List<String> normalizedAddresses = new ArrayList<>();

        for (String address : addresses) {
            if (address == null || address.trim().isEmpty()) {
                continue;
            }

            try {
                String normalizedAddress = normalizeAddress(address);
                if (normalizedAddress != null) {
                    normalizedAddresses.add(normalizedAddress.toLowerCase());
                }
            } catch (Exception e) {
                log.debug("地址标准化失败: address={}, error={}", address, e.getMessage());
                // 标准化失败时保留原始地址
                normalizedAddresses.add(address.toLowerCase());
            }
        }

        return normalizedAddresses;
    }

    /**
     * 标准化单个地址
     */
    private String normalizeAddress(String address) {
        if (address == null) {
            return null;
        }

        address = address.trim();

        // 如果是十六进制格式（41开头的42位或40位），转换为Base58
        if (address.matches("^41[0-9a-fA-F]{40}$") || address.matches("^[0-9a-fA-F]{40}$")) {
            try {
                return org.dromara.common.scanning.utils.TronAddressUtils.hexToAddressFull(address);
            } catch (Exception e) {
                log.debug("十六进制地址转换失败: {}", address);
                return address; // 转换失败时返回原始地址
            }
        }

        // 如果已经是Base58格式（T开头），直接返回
        if (address.startsWith("T") && address.length() >= 30) {
            return address;
        }

        return address;
    }

    /**
     * 检查地址是否在监控集合中
     */
    private boolean containsMonitoredAddress(String address, Set<String> monitoredAddresses) {
        if (address == null || monitoredAddresses.isEmpty()) {
            return false;
        }
        return monitoredAddresses.contains(address.toLowerCase());
    }

    /**
     * 获取监控的合约地址集合
     */
    private Set<String> getMonitoredContractAddresses() {
        return configFacade.getEnabledContractAddresses().stream()
            .map(String::toLowerCase)
            .collect(java.util.stream.Collectors.toSet());
    }

    /**
     * 获取监控的钱包地址集合
     * 重构说明：从TronTransactionManager移动到此处，消除依赖
     */
    private Set<String> getMonitoredWalletAddresses() {
        return getMonitoredAddresses(configFacade).stream()
            .map(String::toLowerCase)
            .collect(java.util.stream.Collectors.toSet());
    }

    /**
     * 获取监控地址集合
     * 从TronTransactionManager移动到此处的方法
     *
     * @param configFacade TRON配置门面
     * @return 监控地址集合
     */
    public Set<String> getMonitoredAddresses(TronConfigFacade configFacade) {
        // 获取TRON监控地址集合，用于性能优化
        return TenantHelper.ignore(tronAddressService::queryAllAddressSet);
    }

    /**
     * 检查交易是否涉及监控地址
     * 从TronTransactionManager移动到此处的方法
     * 用于在完整解析前进行快速过滤
     *
     * @param tronLogModel       TRON日志模型
     * @param monitoredAddresses 监控地址集合
     * @param configFacade       TRON配置门面
     * @return 如果交易涉及监控地址则返回true
     */
    public boolean isMonitoredTransaction(TronLogModel tronLogModel, Set<String> monitoredAddresses, TronConfigFacade configFacade) {
        if (monitoredAddresses == null || monitoredAddresses.isEmpty()) {
            return true; // 如果没有监控地址集合，保守处理所有交易
        }

        try {
            // 快速提取地址
            String[] addresses = extractAddressesFromLog(tronLogModel);
            if (addresses == null || addresses.length != 2) {
                return true; // 地址提取失败，保守处理
            }

            String fromAddress = addresses[0];
            String toAddress = addresses[1];

            // 检查是否涉及监控地址
            boolean isMonitored = monitoredAddresses.contains(fromAddress) || monitoredAddresses.contains(toAddress);

            if (log.isTraceEnabled()) {
                log.trace("{}链交易{}地址检查: from={}, to={}, 是否监控={}",
                    configFacade.getChainName(), tronLogModel.getTransactionHash(), fromAddress, toAddress, isMonitored);
            }

            return isMonitored;

        } catch (Exception e) {
            log.debug("{}链交易{}地址检查失败，保守处理: {}",
                configFacade.getChainName(), tronLogModel.getTransactionHash(), e.getMessage());
            return true; // 检查失败，保守处理
        }
    }

    /**
     * 从TronLogModel中快速提取地址
     * 简化版本的地址提取方法
     *
     * @param tronLogModel TRON日志模型
     * @return 地址数组 [fromAddress, toAddress]，失败时返回null
     */
    private String[] extractAddressesFromLog(TronLogModel tronLogModel) {
        try {
            // TRON的Transfer事件结构与EVM类似
            // 从topics中提取from和to地址
            if (tronLogModel.getTopics() == null || tronLogModel.getTopics().size() < 3) {
                return null;
            }

            // 解析from地址
            String fromAddress = tronLogModel.getTopics().get(1);
            // 解析to地址
            String toAddress = tronLogModel.getTopics().get(2);

            // TRON地址格式转换（如果需要）
            fromAddress = convertTronAddress(fromAddress);
            toAddress = convertTronAddress(toAddress);

            return new String[]{fromAddress.toLowerCase(), toAddress.toLowerCase()};

        } catch (Exception e) {
            log.debug("提取TRON Log地址失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 转换TRON地址格式
     * TRON地址可能需要特殊的格式转换
     */
    private String convertTronAddress(String address) {
        if (address == null) {
            return null;
        }

        // 如果是十六进制格式，可能需要转换为Base58格式
        // 这里先简单处理，具体转换逻辑可以根据实际需要调整
        if (address.startsWith("0x") && address.length() > 26) {
            // 去掉前缀0x000...，保留地址部分
            return address.substring(26);
        }

        return address;
    }

    /**
     * 存储地址过滤结果到处理上下文
     */
    private void storeAddressFilterResult(TransactionModel transactionModel, boolean passed) {
        transactionModel.putContextValue(ADDRESS_FILTER_RESULT_KEY, passed);

        String txId = "unknown";
        if (transactionModel.getTronTransactionModel() != null) {
            txId = transactionModel.getTronTransactionModel().getTxID();
        }

        log.trace("{}链交易{}地址过滤结果已存储: {}",
            configFacade.getChainName(), txId, passed);
    }

    /**
     * 静态方法：获取地址过滤结果
     * 供后续的MonitorEvent使用
     */
    public static Boolean getAddressFilterResult(TransactionModel transactionModel) {
        if (transactionModel == null) {
            return null;
        }
        return transactionModel.getContextValue(ADDRESS_FILTER_RESULT_KEY, Boolean.class);
    }


}
